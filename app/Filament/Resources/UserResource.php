<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\TextColumn;

use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Builder;


final class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'User Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('User Information')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                            ->schema([
                                Section::make('Personal Details')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('name')
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->live(onBlur: true),
                                                TextInput::make('email')
                                                    ->email()
                                                    ->required()
                                                    ->unique(ignoreRecord: true)
                                                    ->maxLength(255),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('phone')
                                                    ->tel()
                                                    ->maxLength(20),
                                                TextInput::make('employee_id')
                                                    ->unique(ignoreRecord: true)
                                                    ->maxLength(50),
                                            ]),
                                        Grid::make(3)
                                            ->schema([
                                                DatePicker::make('date_of_birth')
                                                    ->maxDate(now()->subYears(16)),
                                                Select::make('gender')
                                                    ->options([
                                                        'male' => 'Male',
                                                        'female' => 'Female',
                                                        'other' => 'Other',
                                                        'prefer_not_to_say' => 'Prefer not to say',
                                                    ]),
                                                Select::make('language')
                                                    ->options([
                                                        'en' => 'English',
                                                        'es' => 'Spanish',
                                                        'fr' => 'French',
                                                        'de' => 'German',
                                                        'it' => 'Italian',
                                                        'pt' => 'Portuguese',
                                                        'zh' => 'Chinese',
                                                        'ja' => 'Japanese',
                                                        'ko' => 'Korean',
                                                        'ar' => 'Arabic',
                                                    ])
                                                    ->default('en')
                                                    ->searchable(),
                                            ]),
                                        Textarea::make('bio')
                                            ->maxLength(500)
                                            ->rows(3),
                                        Forms\Components\FileUpload::make('avatar_url')
                                            ->label('Avatar')
                                            ->image()
                                            ->imageEditor()
                                            ->circleCropper()
                                            ->columnSpanFull(),
                                    ]),
                            ]),

                        Tabs\Tab::make('Contact & Address')
                            ->schema([
                                Section::make('Contact Information')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('website')
                                                    ->url()
                                                    ->maxLength(255),
                                                Select::make('timezone')
                                                    ->options([
                                                        'UTC' => 'UTC',
                                                        'America/New_York' => 'Eastern Time',
                                                        'America/Chicago' => 'Central Time',
                                                        'America/Denver' => 'Mountain Time',
                                                        'America/Los_Angeles' => 'Pacific Time',
                                                        'Europe/London' => 'London',
                                                        'Europe/Paris' => 'Paris',
                                                        'Asia/Tokyo' => 'Tokyo',
                                                        'Asia/Shanghai' => 'Shanghai',
                                                        'Australia/Sydney' => 'Sydney',
                                                    ])
                                                    ->default('UTC')
                                                    ->searchable(),
                                            ]),
                                    ]),
                                Section::make('Address')
                                    ->schema([
                                        Textarea::make('address')
                                            ->rows(2)
                                            ->columnSpanFull(),
                                        Grid::make(3)
                                            ->schema([
                                                TextInput::make('city')
                                                    ->maxLength(100),
                                                TextInput::make('state')
                                                    ->maxLength(100),
                                                TextInput::make('postal_code')
                                                    ->maxLength(20),
                                            ]),
                                        TextInput::make('country')
                                            ->maxLength(100),
                                    ]),
                                Section::make('Social Links')
                                    ->schema([
                                        KeyValue::make('social_links')
                                            ->keyLabel('Platform')
                                            ->valueLabel('URL')
                                            ->addActionLabel('Add social link'),
                                    ]),
                            ]),

                        Tabs\Tab::make('Professional')
                            ->schema([
                                Section::make('Work Information')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                TextInput::make('department')
                                                    ->maxLength(100),
                                                TextInput::make('position')
                                                    ->maxLength(100),
                                            ]),
                                        Grid::make(2)
                                            ->schema([
                                                DatePicker::make('hire_date'),
                                                Select::make('manager_id')
                                                    ->relationship('manager', 'name')
                                                    ->searchable()
                                                    ->preload(),
                                            ]),
                                    ]),
                            ]),

                        Tabs\Tab::make('Security & Account')
                            ->schema([
                                Section::make('Authentication')
                                    ->schema([
                                        TextInput::make('password')
                                            ->password()
                                            ->dehydrateStateUsing(fn ($state) => filled($state) ? Hash::make($state) : null)
                                            ->dehydrated(fn ($state) => filled($state))
                                            ->required(fn (string $context): bool => $context === 'create')
                                            ->maxLength(255)
                                            ->revealable(),
                                        Grid::make(2)
                                            ->schema([
                                                Toggle::make('must_change_password')
                                                    ->label('Must change password on next login'),
                                                Toggle::make('two_factor_enabled')
                                                    ->label('Two-factor authentication enabled'),
                                            ]),
                                    ]),
                                Section::make('Account Status')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Select::make('account_status')
                                                    ->options([
                                                        'active' => 'Active',
                                                        'inactive' => 'Inactive',
                                                        'suspended' => 'Suspended',
                                                        'pending' => 'Pending',
                                                        'locked' => 'Locked',
                                                    ])
                                                    ->default('pending')
                                                    ->required(),
                                                Toggle::make('active_status')
                                                    ->label('Active Status')
                                                    ->default(true),
                                            ]),
                                    ]),
                                Section::make('Preferences')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Toggle::make('dark_mode')
                                                    ->label('Dark mode preference'),
                                                TextInput::make('messenger_color')
                                                    ->label('Messenger color')
                                                    ->maxLength(7),
                                            ]),
                                        KeyValue::make('notification_preferences')
                                            ->keyLabel('Notification Type')
                                            ->valueLabel('Enabled')
                                            ->addActionLabel('Add notification preference'),
                                        KeyValue::make('privacy_settings')
                                            ->keyLabel('Privacy Setting')
                                            ->valueLabel('Value')
                                            ->addActionLabel('Add privacy setting'),
                                    ]),
                            ]),

                        Tabs\Tab::make('Roles & Permissions')
                            ->schema([
                                Section::make('Role Assignment')
                                    ->schema([
                                        Select::make('roles')
                                            ->label('Roles')
                                            ->multiple()
                                            ->relationship('roles', 'name')
                                            ->preload()
                                            ->searchable()
                                            ->columnSpanFull(),
                                    ]),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('avatar_url')
                    ->label('Avatar')
                    ->circular()
                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->name) . '&color=7F9CF5&background=EBF4FF')
                    ->size(40),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),
                TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->icon('heroicon-m-envelope'),
                TextColumn::make('phone')
                    ->searchable()
                    ->toggleable()
                    ->icon('heroicon-m-phone'),
                TextColumn::make('account_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'gray',
                        'suspended', 'locked' => 'danger',
                        'pending' => 'warning',
                        default => 'gray',
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'active' => 'heroicon-o-check-circle',
                        'suspended', 'locked' => 'heroicon-o-x-circle',
                        'pending' => 'heroicon-o-clock',
                        'inactive' => 'heroicon-o-minus-circle',
                        default => 'heroicon-o-question-mark-circle',
                    }),
                TextColumn::make('roles.name')
                    ->label('Roles')
                    ->badge()
                    ->searchable()
                    ->separator(','),
                TextColumn::make('department')
                    ->searchable()
                    ->toggleable()
                    ->icon('heroicon-m-building-office'),
                TextColumn::make('position')
                    ->searchable()
                    ->toggleable(),
                IconColumn::make('two_factor_enabled')
                    ->label('2FA')
                    ->boolean()
                    ->toggleable(),
                TextColumn::make('last_login_at')
                    ->label('Last Login')
                    ->dateTime()
                    ->sortable()
                    ->since()
                    ->toggleable(),
                IconColumn::make('active_status')
                    ->label('Active')
                    ->boolean()
                    ->toggleable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('account_status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'suspended' => 'Suspended',
                        'pending' => 'Pending',
                        'locked' => 'Locked',
                    ])
                    ->multiple(),
                SelectFilter::make('roles')
                    ->relationship('roles', 'name')
                    ->multiple()
                    ->preload(),
                SelectFilter::make('department')
                    ->options(fn () => User::whereNotNull('department')->distinct()->pluck('department', 'department')->toArray()),
                Filter::make('two_factor_enabled')
                    ->query(fn (Builder $query): Builder => $query->where('two_factor_enabled', true))
                    ->label('2FA Enabled'),
                Filter::make('last_login')
                    ->form([
                        DatePicker::make('last_login_from')
                            ->label('Last login from'),
                        DatePicker::make('last_login_until')
                            ->label('Last login until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['last_login_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('last_login_at', '>=', $date),
                            )
                            ->when(
                                $data['last_login_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('last_login_at', '<=', $date),
                            );
                    }),
                Filter::make('profile_incomplete')
                    ->query(fn (Builder $query): Builder => $query->whereNull('profile_completed_at'))
                    ->label('Incomplete Profiles'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                ActionGroup::make([
                    Action::make('activate')
                        ->label('Activate')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->visible(fn (User $record): bool => !$record->isActive())
                        ->action(fn (User $record) => $record->activate()),
                    Action::make('deactivate')
                        ->label('Deactivate')
                        ->icon('heroicon-o-x-circle')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->visible(fn (User $record): bool => $record->isActive())
                        ->action(fn (User $record) => $record->deactivate()),
                    Action::make('suspend')
                        ->label('Suspend')
                        ->icon('heroicon-o-no-symbol')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->visible(fn (User $record): bool => !$record->isSuspended())
                        ->action(fn (User $record) => $record->suspend()),
                    Action::make('unlock')
                        ->label('Unlock')
                        ->icon('heroicon-o-lock-open')
                        ->color('success')
                        ->requiresConfirmation()
                        ->visible(fn (User $record): bool => $record->isLocked())
                        ->action(fn (User $record) => $record->unlock()),
                    Action::make('resetPassword')
                        ->label('Reset Password')
                        ->icon('heroicon-o-key')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->action(function (User $record): void {
                            $newPassword = \Illuminate\Support\Str::random(12);
                            $record->updatePassword($newPassword);

                            Notification::make()
                                ->title('Password Reset')
                                ->body("Password has been reset to: {$newPassword}")
                                ->success()
                                ->sendToDatabase($record);
                        }),
                    Action::make('forcePasswordChange')
                        ->label('Force Password Change')
                        ->icon('heroicon-o-shield-exclamation')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->action(fn (User $record) => $record->update(['must_change_password' => true])),
                    Action::make('sendWelcomeEmail')
                        ->label('Send Welcome Email')
                        ->icon('heroicon-o-envelope')
                        ->color('info')
                        ->action(function (User $record): void {
                            // TODO: Implement welcome email sending
                            Notification::make()
                                ->title('Welcome Email Sent')
                                ->body("Welcome email sent to {$record->email}")
                                ->success()
                                ->send();
                        }),
                    Action::make('viewActivity')
                        ->label('View Activity')
                        ->icon('heroicon-o-eye')
                        ->color('gray')
                        ->action(function (User $record): void {
                            // TODO: Implement activity view functionality
                            Notification::make()
                                ->title('Activity View')
                                ->body("Viewing activity for {$record->name}")
                                ->info()
                                ->send();
                        }),
                ])
                    ->label('Actions')
                    ->icon('heroicon-m-ellipsis-vertical')
                    ->size('sm')
                    ->color('gray'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(fn ($records) => $records->each->activate()),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->action(fn ($records) => $records->each->deactivate()),
                    Tables\Actions\BulkAction::make('suspend')
                        ->label('Suspend Selected')
                        ->icon('heroicon-o-no-symbol')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->action(fn ($records) => $records->each->suspend()),
                    Tables\Actions\BulkAction::make('exportSelected')
                        ->label('Export Selected')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->color('info')
                        ->action(function ($records): void {
                            // TODO: Implement export functionality
                            $count = $records->count();
                            Notification::make()
                                ->title('Export Started')
                                ->body("Export of {$count} users has been queued for processing")
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->persistSortInSession()
            ->persistSearchInSession()
            ->persistFiltersInSession();
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
