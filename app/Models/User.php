<?php

declare(strict_types=1);

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use Exception;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasAvatar;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\HasApiTokens; // Import the trait
use Rawilk\ProfileFilament\Concerns\TwoFactorAuthenticatable;
use Spatie\Permission\Traits\HasRoles;

final class User extends Authenticatable implements FilamentUser, HasAvatar
{
    // use  HasPanelShield;

    use HasApiTokens,
        HasFactory,
        HasRoles,
        Notifiable,
        TwoFactorAuthenticatable; // Add the trait

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar_url',
        'phone',
        'timezone',
        'language',
        'last_login_at',
        'last_login_ip',
        'account_status',
        'profile_completed_at',
        'email_verified_at',
        'phone_verified_at',
        'two_factor_enabled',
        'login_attempts',
        'locked_until',
        'password_changed_at',
        'must_change_password',
        'notification_preferences',
        'privacy_settings',
        'date_of_birth',
        'gender',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'bio',
        'website',
        'social_links',
        'department',
        'position',
        'employee_id',
        'hire_date',
        'manager_id',
        'active_status',
        'dark_mode',
        'messenger_color',
        'avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = ['password', 'remember_token'];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'password' => 'hashed',
        'last_login_at' => 'datetime',
        'profile_completed_at' => 'datetime',
        'locked_until' => 'datetime',
        'password_changed_at' => 'datetime',
        'date_of_birth' => 'date',
        'hire_date' => 'date',
        'two_factor_enabled' => 'boolean',
        'must_change_password' => 'boolean',
        'active_status' => 'boolean',
        'dark_mode' => 'boolean',
        'notification_preferences' => 'array',
        'privacy_settings' => 'array',
        'social_links' => 'array',
        'login_attempts' => 'integer',
        'manager_id' => 'integer',
    ];

    public function getFilamentAvatarUrl(): ?string
    {
        if ($this->avatar_url) {
            try {
                if (Storage::disk('supabase')->exists($this->avatar_url)) {
                    return Storage::disk('supabase')->url($this->avatar_url);
                }
            } catch (Exception) {
                // Fallback to gravatar if there's an SSL or other error
            }
        }

        // Default to gravatar
        $hash = md5(mb_strtolower(mb_trim($this->email)));

        return 'https://www.gravatar.com/avatar/'.$hash.'?d=mp&r=g&s=250';
    }

    public function canComment(): bool
    {
        return true;
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'admin') {
            return $this->hasAnyRole([
                'cashier',
                'panel_user',
                'admin',
                'super_admin',
            ]);
        }
        if ($panel->getId() === 'faculty') {
            // Only allow access if the faculty panel is enabled in config
            if (! config('app.enable_faculty_panel', false)) {
                return false;
            }

            return $this->hasRole('faculty');
        }

        return $this->hasRole('super_admin');
    }

    // admin Transactions

    public function transactions()
    {
        return $this->hasMany(AdminTransaction::class, 'admin_id', 'id');
    }

    public function getIsSuperAdminAttribute(): bool
    {
        return $this->hasRole('super_admin');
    }

    public function getIsCashierAttribute(): bool
    {
        return $this->hasRole('Cashier');
    }

    public function getIsRegistrarAttribute(): bool
    {
        return $this->hasRole('registrar');
    }

    public function getIsDeptHeadAttribute(): bool
    {
        return $this->hasAnyRole([
            'IT-head-dept',
            'BA-head-dept',
            'HM-head-dept',
        ]);
    }

    public function getViewableCoursesAttribute(): array
    {
        $courses = [];

        if ($this->can('view_IT_students_students')) {
            $courses = array_merge($courses, [1, 6, 10, 13]);
        }

        if ($this->can('view_BA_students_students')) {
            $courses = array_merge($courses, [4, 5, 8, 9]);
        }

        if ($this->can('view_HM_students_students')) {
            return array_merge($courses, [2, 3, 11, 12]);
        }

        return $courses;
    }

    // Account Status Methods
    public function isActive(): bool
    {
        return $this->account_status === 'active';
    }

    public function isInactive(): bool
    {
        return $this->account_status === 'inactive';
    }

    public function isSuspended(): bool
    {
        return $this->account_status === 'suspended';
    }

    public function isPending(): bool
    {
        return $this->account_status === 'pending';
    }

    public function isLocked(): bool
    {
        return $this->account_status === 'locked' || ($this->locked_until && $this->locked_until->isFuture());
    }

    public function activate(): void
    {
        $this->update(['account_status' => 'active']);
    }

    public function deactivate(): void
    {
        $this->update(['account_status' => 'inactive']);
    }

    public function suspend(): void
    {
        $this->update(['account_status' => 'suspended']);
    }

    public function lock(?\Carbon\Carbon $until = null): void
    {
        $this->update([
            'account_status' => 'locked',
            'locked_until' => $until,
        ]);
    }

    public function unlock(): void
    {
        $this->update([
            'account_status' => 'active',
            'locked_until' => null,
            'login_attempts' => 0,
        ]);
    }

    // Profile Completion Methods
    public function isProfileComplete(): bool
    {
        return !is_null($this->profile_completed_at);
    }

    public function getProfileCompletionPercentage(): int
    {
        $requiredFields = ['name', 'email', 'phone', 'date_of_birth'];
        $optionalFields = ['bio', 'website', 'address', 'city', 'country'];

        $completedRequired = 0;
        $completedOptional = 0;

        foreach ($requiredFields as $field) {
            if (!empty($this->$field)) {
                $completedRequired++;
            }
        }

        foreach ($optionalFields as $field) {
            if (!empty($this->$field)) {
                $completedOptional++;
            }
        }

        $requiredPercentage = ($completedRequired / count($requiredFields)) * 70; // 70% for required
        $optionalPercentage = ($completedOptional / count($optionalFields)) * 30; // 30% for optional

        return (int) ($requiredPercentage + $optionalPercentage);
    }

    public function markProfileAsComplete(): void
    {
        if ($this->getProfileCompletionPercentage() >= 70) {
            $this->update(['profile_completed_at' => now()]);
        }
    }

    // Authentication & Security Methods
    public function recordLoginAttempt(string $ip): void
    {
        $this->increment('login_attempts');
        $this->update(['last_login_ip' => $ip]);

        if ($this->login_attempts >= 5) {
            $this->lock(now()->addMinutes(30));
        }
    }

    public function recordSuccessfulLogin(string $ip): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip,
            'login_attempts' => 0,
            'locked_until' => null,
        ]);
    }

    public function needsPasswordChange(): bool
    {
        return $this->must_change_password ||
               ($this->password_changed_at && $this->password_changed_at->diffInDays(now()) > 90);
    }

    public function updatePassword(string $password): void
    {
        $this->update([
            'password' => $password,
            'password_changed_at' => now(),
            'must_change_password' => false,
        ]);
    }

    // Relationship Methods
    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function subordinates()
    {
        return $this->hasMany(User::class, 'manager_id');
    }

    public function userSettings()
    {
        return $this->hasOne(\App\Models\UserSetting::class);
    }

    public function activityLogs()
    {
        return $this->hasMany(\App\Models\ActivityLog::class, 'causer_id');
    }

    public function authenticationLogs()
    {
        return $this->hasMany(\App\Models\AuthenticationLog::class, 'authenticatable_id')
                    ->where('authenticatable_type', self::class);
    }

    // Notification Preference Methods
    public function getNotificationPreference(string $type): bool
    {
        $preferences = $this->notification_preferences ?? [];
        return $preferences[$type] ?? true; // Default to enabled
    }

    public function setNotificationPreference(string $type, bool $enabled): void
    {
        $preferences = $this->notification_preferences ?? [];
        $preferences[$type] = $enabled;
        $this->update(['notification_preferences' => $preferences]);
    }

    public function getDefaultNotificationPreferences(): array
    {
        return [
            'email_login_alerts' => true,
            'email_password_changes' => true,
            'email_profile_updates' => false,
            'email_system_notifications' => true,
            'email_marketing' => false,
            'sms_security_alerts' => false,
            'push_notifications' => true,
            'desktop_notifications' => true,
        ];
    }

    // Privacy Settings Methods
    public function getPrivacySetting(string $setting): bool
    {
        $settings = $this->privacy_settings ?? [];
        return $settings[$setting] ?? false; // Default to private
    }

    public function setPrivacySetting(string $setting, bool $value): void
    {
        $settings = $this->privacy_settings ?? [];
        $settings[$setting] = $value;
        $this->update(['privacy_settings' => $settings]);
    }

    public function getDefaultPrivacySettings(): array
    {
        return [
            'profile_visible_to_public' => false,
            'email_visible_to_users' => false,
            'phone_visible_to_users' => false,
            'show_online_status' => true,
            'allow_direct_messages' => true,
            'show_in_directory' => true,
            'data_processing_consent' => false,
            'marketing_consent' => false,
        ];
    }

    // Utility Methods
    public function getFullNameAttribute(): string
    {
        return $this->name;
    }

    public function getInitialsAttribute(): string
    {
        $names = explode(' ', $this->name);
        $initials = '';
        foreach ($names as $name) {
            $initials .= strtoupper(substr($name, 0, 1));
        }
        return $initials;
    }

    public function getAgeAttribute(): ?int
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }

    public function getTimeInCompanyAttribute(): ?string
    {
        if (!$this->hire_date) {
            return null;
        }

        $diff = $this->hire_date->diff(now());
        $years = $diff->y;
        $months = $diff->m;

        if ($years > 0) {
            return $years . ' year' . ($years > 1 ? 's' : '') .
                   ($months > 0 ? ', ' . $months . ' month' . ($months > 1 ? 's' : '') : '');
        }

        return $months . ' month' . ($months > 1 ? 's' : '');
    }

    public function isOnline(): bool
    {
        return $this->last_login_at && $this->last_login_at->diffInMinutes(now()) < 15;
    }

    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->account_status) {
            'active' => 'success',
            'inactive' => 'gray',
            'suspended' => 'danger',
            'pending' => 'warning',
            'locked' => 'danger',
            default => 'gray',
        };
    }

    public function getViewTitleCourseAttribute(): string
    {
        $titles = [];

        if ($this->can('view_IT_students_students')) {
            $titles[] = 'IT';
        }

        if ($this->can('view_BA_students_students')) {
            $titles[] = 'BA';
        }

        if ($this->can('view_HM_students_students')) {
            $titles[] = 'HM';
        }

        return implode(', ', $titles);
    }

    // Dpublic function notifications()
    // {
    //     return $this->morphMany(
    //         DatabaseNotification::class,
    //         "notifiable"
    //     )->orderBy("created_at", "desc");
    // }
}
